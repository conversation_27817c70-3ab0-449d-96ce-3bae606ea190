#!/usr/bin/env python3
"""
Comprehensive test script to verify wallet behavior matches original arlocal
"""

import requests
import json
import sys

def test_wallet_endpoint(address, expected_balance, test_name):
    """Test wallet balance endpoint"""
    base_url = "http://localhost:1984"
    
    try:
        response = requests.get(f"{base_url}/wallet/{address}/balance")
        
        if response.status_code == 200:
            if response.text == expected_balance:
                print(f"✅ {test_name}: Expected '{expected_balance}', got '{response.text}' - PASSED")
                return True
            else:
                print(f"❌ {test_name}: Expected '{expected_balance}', got '{response.text}' - FAILED")
                return False
        else:
            print(f"❌ {test_name}: Expected status 200, got {response.status_code} - FAILED")
            return False
            
    except Exception as e:
        print(f"❌ {test_name}: Error - {e}")
        return False

def test_invalid_address():
    """Test with invalid address format"""
    base_url = "http://localhost:1984"
    invalid_address = "invalid-address"
    
    try:
        response = requests.get(f"{base_url}/wallet/{invalid_address}/balance")
        
        if response.status_code == 422:
            error_data = response.json()
            if "Address badly formatted" in error_data.get("error", ""):
                print("✅ Invalid address test: Returns 422 with correct error - PASSED")
                return True
            else:
                print(f"❌ Invalid address test: Wrong error message - {error_data}")
                return False
        else:
            print(f"❌ Invalid address test: Expected status 422, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Invalid address test: Error - {e}")
        return False

def test_reset_functionality():
    """Test that reset clears wallet balances"""
    base_url = "http://localhost:1984"
    test_address = "test-reset-wallet-address-123456789012345"  # Exactly 43 chars
    
    try:
        # First mint some tokens
        mint_response = requests.get(f"{base_url}/mint/{test_address}/500")
        if mint_response.status_code != 200:
            print("❌ Reset test: Failed to mint tokens")
            return False
        
        # Check balance
        balance_response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        if balance_response.text != "500":
            print(f"❌ Reset test: Expected balance '500', got '{balance_response.text}'")
            return False
        
        # Reset network
        reset_response = requests.get(f"{base_url}/reset")
        if reset_response.status_code != 200:
            print("❌ Reset test: Failed to reset network")
            return False
        
        # Check balance after reset (should be 0)
        balance_after_reset = requests.get(f"{base_url}/wallet/{test_address}/balance")
        if balance_after_reset.text == "0":
            print("✅ Reset test: Balance correctly reset to '0' - PASSED")
            return True
        else:
            print(f"❌ Reset test: Expected balance '0' after reset, got '{balance_after_reset.text}'")
            return False
            
    except Exception as e:
        print(f"❌ Reset test: Error - {e}")
        return False

if __name__ == "__main__":
    print("ArLocal Python Wallet Comprehensive Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Non-existent wallet (using proper 43-char address)
    total_tests += 1
    if test_wallet_endpoint("pLPrkgQBFCpaJm1DIksYSPaeMNy6evqoLPCPAlZ-YG8", "0", "Non-existent wallet"):
        tests_passed += 1

    # Test 2: Invalid address format
    total_tests += 1
    if test_invalid_address():
        tests_passed += 1

    # Test 3: Mint and check balance
    total_tests += 1
    test_address = "test-wallet-address-1234567890123456789012"  # Exactly 43 chars
    
    # First check it's 0
    if test_wallet_endpoint(test_address, "0", "Before mint"):
        # Mint 250 tokens
        try:
            mint_response = requests.get(f"http://localhost:1984/mint/{test_address}/250")
            if mint_response.status_code == 200:
                # Check balance after mint
                if test_wallet_endpoint(test_address, "250", "After mint"):
                    tests_passed += 1
                else:
                    print("❌ Mint test: Balance check failed")
            else:
                print(f"❌ Mint test: Mint failed with status {mint_response.status_code}")
        except Exception as e:
            print(f"❌ Mint test: Error - {e}")
    
    # Test 4: Reset functionality
    total_tests += 1
    if test_reset_functionality():
        tests_passed += 1
    
    # Test 5: Check that the test address is back to 0 after reset
    total_tests += 1
    if test_wallet_endpoint(test_address, "0", "After reset"):
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST SUMMARY:")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Wallet handling is now identical to original arlocal!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
