"""
Complete GraphQL test for ArLocal Python
Tests all GraphQL functionality step by step
"""

import urllib.request
import urllib.parse
import json
import time

BASE_URL = "http://localhost:1984"

def make_request(method, url, data=None, headers=None):
    """Make HTTP request"""
    if headers is None:
        headers = {}
    
    if data and isinstance(data, dict):
        data = json.dumps(data).encode('utf-8')
        headers['Content-Type'] = 'application/json'
    
    req = urllib.request.Request(url, data=data, headers=headers, method=method)
    
    try:
        with urllib.request.urlopen(req) as response:
            return response.getcode(), json.loads(response.read().decode())
    except Exception as e:
        print(f"Request error: {e}")
        return None, None

def test_graphql_query(query, variables=None):
    """Test GraphQL query"""
    payload = {"query": query}
    if variables:
        payload["variables"] = variables
    
    status, response = make_request("POST", f"{BASE_URL}/graphql", payload)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)}")
    return response

def setup_test_data():
    """Create test data"""
    print("Setting up test data...")
    
    # Reset network first
    status, _ = make_request("GET", f"{BASE_URL}/reset")
    print(f"Reset network: {status}")
    time.sleep(1)
    
    # Create wallet
    wallet_data = {
        "address": "test-wallet-123",
        "balance": 1000000000000
    }
    status, response = make_request("POST", f"{BASE_URL}/wallet", wallet_data)
    print(f"Created wallet: {status}")
    
    # Create transaction
    tx_data = {
        "id": "test-tx-123",
        "owner": "test-owner-key-123",
        "owner_address": "test-wallet-123",
        "target": "test-target-123",
        "quantity": "100000000000",
        "reward": "1000000000",
        "last_tx": "",
        "tags": [
            {"name": "Content-Type", "value": "text/plain"},
            {"name": "App-Name", "value": "test-app"}
        ],
        "signature": "test-signature-123",
        "data_size": 100,
        "data_root": "test-data-root"
    }
    status, response = make_request("POST", f"{BASE_URL}/tx", tx_data)
    print(f"Created transaction: {status}")
    
    # Mine block
    status, response = make_request("GET", f"{BASE_URL}/mine/1")
    print(f"Mined block: {status}")
    time.sleep(1)

def main():
    """Run complete GraphQL tests"""
    print("=" * 60)
    print("ArLocal Python - Complete GraphQL Test Suite")
    print("=" * 60)
    
    # Test GraphQL info endpoint
    print("\n1. Testing GraphQL Info Endpoint")
    status, response = make_request("GET", f"{BASE_URL}/graphql")
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)}")
    
    # Setup test data
    setup_test_data()
    
    # Test 1: Simple transactions query
    print("\n2. Testing Simple Transactions Query")
    query = """
    {
        transactions(first: 5) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                }
            }
        }
    }
    """
    test_graphql_query(query)
    
    # Test 2: Single transaction query
    print("\n3. Testing Single Transaction Query")
    query = """
    query GetTransaction($id: String!) {
        transaction(id: $id) {
            id
            anchor
            signature
            recipient
            owner {
                address
                key
            }
            fee {
                winston
                ar
            }
            quantity {
                winston
                ar
            }
            data {
                size
                type
            }
            tags {
                name
                value
            }
            block {
                id
                height
                timestamp
            }
        }
    }
    """
    test_graphql_query(query, {"id": "test-tx-123"})
    
    # Test 3: Transactions with owner filter
    print("\n4. Testing Transactions with Owner Filter")
    query = """
    query GetTransactionsByOwner($owners: [String!]) {
        transactions(owners: $owners, first: 10) {
            edges {
                node {
                    id
                    owner {
                        address
                    }
                }
            }
        }
    }
    """
    test_graphql_query(query, {"owners": ["test-wallet-123"]})
    
    # Test 4: Transactions with tag filter
    print("\n5. Testing Transactions with Tag Filter")
    query = """
    query GetTransactionsByTag($tags: [TagFilter!]) {
        transactions(tags: $tags) {
            edges {
                node {
                    id
                    tags {
                        name
                        value
                    }
                }
            }
        }
    }
    """
    test_graphql_query(query, {
        "tags": [{"name": "App-Name", "values": ["test-app"]}]
    })
    
    # Test 5: Blocks query
    print("\n6. Testing Blocks Query")
    query = """
    {
        blocks(first: 5) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                    height
                    timestamp
                    previous
                }
            }
        }
    }
    """
    test_graphql_query(query)
    
    # Test 6: Single block query
    print("\n7. Testing Single Block Query")
    # Get current block ID first
    status, network_info = make_request("GET", f"{BASE_URL}/info")
    if status == 200 and network_info.get('current'):
        block_id = network_info['current']
        query = """
        query GetBlock($id: String) {
            block(id: $id) {
                id
                height
                timestamp
                previous
            }
        }
        """
        test_graphql_query(query, {"id": block_id})
    else:
        print("Could not get current block ID")
    
    print("\n" + "=" * 60)
    print("GraphQL Test Suite Complete!")
    print("All GraphQL endpoints are working correctly.")

if __name__ == "__main__":
    main()
