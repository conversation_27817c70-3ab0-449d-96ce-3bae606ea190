#!/usr/bin/env python3
"""
Test script for ArLocal Python API
Demonstrates basic functionality of the ArLocal Python implementation
"""

import requests
import json
import time

BASE_URL = "http://localhost:1984"

def test_endpoint(method, endpoint, data=None, expected_status=200):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n{method} {endpoint}")
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "PATCH":
            response = requests.patch(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)
        else:
            print(f"Unsupported method: {method}")
            return None
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == expected_status:
            print("✓ Success")
        else:
            print("✗ Unexpected status code")
        
        # Try to parse JSON response
        try:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            return result
        except:
            # Not JSON, return text
            text = response.text
            print(f"Response: {text}")
            return text
            
    except requests.exceptions.ConnectionError:
        print("✗ Connection failed - make sure the server is running")
        return None
    except Exception as e:
        print(f"✗ Error: {e}")
        return None

def main():
    """Run API tests"""
    print("ArLocal Python API Test")
    print("=" * 40)
    
    # Test basic status endpoints
    print("\n1. Testing Status Endpoints")
    test_endpoint("GET", "/")
    test_endpoint("GET", "/info")
    test_endpoint("GET", "/peers")
    test_endpoint("GET", "/logs")
    
    # Test transaction anchor
    print("\n2. Testing Transaction Endpoints")
    test_endpoint("GET", "/tx_anchor")
    test_endpoint("GET", "/price/1000")
    test_endpoint("GET", "/tx/pending")
    
    # Test wallet creation
    print("\n3. Testing Wallet Endpoints")
    wallet_data = {"balance": 1000000}
    wallet = test_endpoint("POST", "/wallet", wallet_data)
    
    if wallet and "address" in wallet:
        address = wallet["address"]
        print(f"Created wallet: {address}")
        
        # Test wallet balance
        test_endpoint("GET", f"/wallet/{address}/balance")
        
        # Test minting tokens
        test_endpoint("GET", f"/mint/{address}/500000")
        
        # Check balance again
        test_endpoint("GET", f"/wallet/{address}/balance")
    
    # Test transaction submission
    print("\n4. Testing Transaction Submission")
    tx_data = {
        "owner": "test-owner-key-12345678901234567890123456789012345",
        "target": "test-target-address-1234567890123456789012345",
        "quantity": "0",
        "reward": "1000",
        "last_tx": "",
        "tags": [{"name": "Content-Type", "value": "text/plain"}],
        "signature": "test-signature",
        "data": "Hello, ArLocal Python!",
        "data_size": 21
    }
    
    txid = test_endpoint("POST", "/tx", tx_data)
    
    if txid and isinstance(txid, str):
        print(f"Submitted transaction: {txid}")
        
        # Test transaction retrieval
        test_endpoint("GET", f"/tx/{txid}")
        test_endpoint("GET", f"/tx/{txid}/status")
        test_endpoint("GET", f"/tx/{txid}/data")
        test_endpoint("GET", f"/tx/{txid}/owner")
        
        # Test pending transactions
        test_endpoint("GET", "/tx/pending")
        
        # Test mining
        print("\n5. Testing Mining")
        test_endpoint("GET", "/mine/1")
        
        # Check transaction status after mining
        test_endpoint("GET", f"/tx/{txid}/status")
        
        # Test data access
        print("\n6. Testing Data Access")
        test_endpoint("GET", f"/{txid}")
    
    # Test chunk endpoints
    print("\n7. Testing Chunk Endpoints")
    chunk_data = {
        "chunk": "SGVsbG8gQ2h1bmsh",  # Base64 encoded "Hello Chunks!"
        "data_root": "test-data-root",
        "data_size": 13,
        "data_path": "test-path"
    }
    test_endpoint("POST", "/chunk", chunk_data)
    
    # Try to get chunk (offset will be calculated)
    test_endpoint("GET", "/chunk/13")
    
    # Test block endpoints
    print("\n8. Testing Block Endpoints")
    status = test_endpoint("GET", "/")
    if status and "current" in status:
        current_block = status["current"]
        test_endpoint("GET", f"/block/hash/{current_block}")
        test_endpoint("GET", "/block/height/1")
    
    # Test GraphQL placeholder
    print("\n9. Testing GraphQL Endpoint")
    test_endpoint("GET", "/graphql")
    
    graphql_query = {
        "query": "{ transaction(id: \"test\") { id } }"
    }
    test_endpoint("POST", "/graphql", graphql_query)
    
    # Test reset
    print("\n10. Testing Reset")
    test_endpoint("GET", "/reset")
    
    print("\n" + "=" * 40)
    print("API Test Complete!")
    print("\nTo run this test:")
    print("1. Start the server: python app.py")
    print("2. Run this test: python test_api.py")

if __name__ == "__main__":
    main()
