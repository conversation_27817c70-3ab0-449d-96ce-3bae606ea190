"""
Wallet routes blueprint for ArLocal Python Implementation
Handles wallet creation, balance management, and minting routes
"""

from flask import Blueprint, request, jsonify, Response, current_app
import logging
from utils import random_id, validate_address

# Create blueprint
wallet_bp = Blueprint('wallet', __name__)

# Get logger
logger = logging.getLogger(__name__)

# Logs are accessed via current_app.logs

def format_balance(balance: float) -> str:
    """Format balance as integer if it's a whole number, otherwise as float"""
    if balance == int(balance):
        return str(int(balance))
    return str(balance)

# Wallet Routes
@wallet_bp.route('/wallet', methods=['POST'])
def create_wallet():
    """Create a new wallet"""
    try:
        db = current_app.db
        
        data = request.get_json() or {}

        # Generate address if not provided
        address = data.get('address', random_id(43))

        # Validate address format
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        # Create wallet in database
        balance = float(data.get('balance', 0))
        if db.create_wallet(address, balance):
            wallet = {
                "address": address,
                "balance": balance
            }
            current_app.logs.append(f"Wallet created: {address}")
            return jsonify(wallet)
        else:
            return jsonify({"error": "Failed to create wallet"}), 500

    except Exception as e:
        logger.error(f"Error creating wallet: {e}")
        return jsonify({"error": str(e)}), 500

@wallet_bp.route('/wallet/<address>/balance', methods=['GET'])
def get_wallet_balance_route(address: str):
    """Get wallet balance"""
    db = current_app.db
    
    balance = db.get_wallet_balance(address)
    return Response(format_balance(balance), mimetype='text/plain')

@wallet_bp.route('/wallet/<address>/balance', methods=['PATCH'])
def update_wallet_balance_route(address: str):
    """Update wallet balance"""
    try:
        db = current_app.db
        
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        data = request.get_json()
        if not data or 'balance' not in data:
            return jsonify({"status": 422, "error": "Balance is required !"}), 422

        balance = float(data['balance'])
        if db.update_wallet_balance(address, balance):
            current_app.logs.append(f"Wallet balance updated: {address} -> {balance}")
            return jsonify(data)
        else:
            return jsonify({"error": "Failed to update wallet balance"}), 500

    except Exception as e:
        logger.error(f"Error updating wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@wallet_bp.route('/mint/<address>/<int:balance>', methods=['GET'])
def add_wallet_balance(address: str, balance: int):
    """Add balance to wallet (mint tokens)"""
    try:
        db = current_app.db
        
        new_balance = db.increment_wallet_balance(address, float(balance))
        current_app.logs.append(f"Minted {balance} tokens for {address}")
        return Response(format_balance(new_balance), mimetype='text/plain')

    except Exception as e:
        logger.error(f"Error adding wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@wallet_bp.route('/wallet/<address>/last_tx', methods=['GET'])
def get_wallet_last_transaction(address: str):
    """Get last transaction for wallet"""
    db = current_app.db
    
    last_tx = db.get_wallet_last_transaction(address)
    return Response(last_tx or '', mimetype='text/plain')
