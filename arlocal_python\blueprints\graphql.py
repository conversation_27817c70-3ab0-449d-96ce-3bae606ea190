"""
GraphQL routes blueprint for ArLocal Python Implementation
Handles GraphQL endpoint and schema
"""

from flask import Blueprint, request, jsonify, current_app
import logging
from graphql_schema import schema

# Create blueprint
graphql_bp = Blueprint('graphql', __name__)

# Get logger
logger = logging.getLogger(__name__)

# GraphQL endpoint
@graphql_bp.route('/graphql', methods=['POST', 'GET'])
def graphql_endpoint():
    """GraphQL endpoint with full Arweave API compatibility"""
    if request.method == 'GET':
        # Return GraphQL playground/introspection info
        return jsonify({
            "message": "ArLocal GraphQL endpoint",
            "available_queries": ["transaction", "transactions", "block", "blocks"],
            "schema_url": "/graphql",
            "playground": "Send POST requests with GraphQL queries"
        })

    # POST request - handle GraphQL queries
    try:
        db = current_app.db
        
        data = request.get_json()
        if not data:
            return jsonify({"errors": [{"message": "No JSON data provided"}]}), 400

        query = data.get('query', '')
        variables = data.get('variables', {})
        operation_name = data.get('operationName')

        if not query:
            return jsonify({"errors": [{"message": "No query provided"}]}), 400

        # Execute GraphQL query
        context = {
            'db': db,
            'request': request
        }

        result = schema.execute(
            query,
            variables=variables,
            context=context,
            operation_name=operation_name
        )

        # Format response
        response_data = {}
        if result.data:
            response_data['data'] = result.data

        if result.errors:
            response_data['errors'] = [
                {"message": str(error), "locations": getattr(error, 'locations', None)}
                for error in result.errors
            ]

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"GraphQL execution error: {e}")
        return jsonify({"errors": [{"message": f"Internal server error: {str(e)}"}]}), 500
