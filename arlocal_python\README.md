# ArLocal Python Implementation

A Flask-based reimplementation of the ArLocal Arweave local testnet server.

## Features

This Python implementation provides all the core REST API endpoints from the original ArLocal project:

### Status & Network
- `GET /` - Network status
- `GET /info` - Network information  
- `GET /peers` - Peer list
- `GET /logs` - Application logs
- `GET /reset` - Reset network state

### Transactions
- `GET /tx_anchor` - Get transaction anchor
- `GET /price/<bytes>` - Calculate transaction price
- `GET /tx/pending` - Get pending transactions
- `GET /tx/<txid>` - Get transaction by ID
- `GET /tx/<txid>/status` - Get transaction status
- `GET /tx/<txid>/offset` - Get transaction offset
- `GET /tx/<txid>/data` - Get raw transaction data
- `GET /tx/<txid>/data.<ext>` - Get transaction data with extension
- `GET /tx/<txid>/<field>` - Get specific transaction field
- `POST /tx` - Submit new transaction
- `DELETE /tx/<txid>` - Delete transaction

### Blocks
- `GET /block/hash/<hash>` - Get block by hash
- `GET /block/height/<height>` - Get block by height

### Mining
- `GET /mine/<qty>` - Mine blocks
- `GET /mineWithFails/<qty>` - Mine blocks with failure simulation

### Wallets
- `POST /wallet` - Create new wallet
- `GET /wallet/<address>/balance` - Get wallet balance
- `PATCH /wallet/<address>/balance` - Update wallet balance
- `GET /mint/<address>/<amount>` - Mint tokens to wallet
- `GET /wallet/<address>/last_tx` - Get last wallet transaction

### Chunks
- `POST /chunk` - Submit chunk data
- `GET /chunk/<offset>` - Get chunk by offset

### Data Access
- `GET /<txid>` - Get transaction data by ID
- `HEAD /<txid>` - Get transaction data headers

### GraphQL
- `POST /graphql` - GraphQL endpoint (full Arweave API compatibility)
- `GET /graphql` - GraphQL schema information

#### GraphQL Features
- **Complete Arweave GraphQL API** - Full compatibility with Arweave's GraphQL schema
- **Transaction Queries** - Get single transactions or paginated lists with filtering
- **Block Queries** - Query blocks by ID or get paginated lists
- **Advanced Filtering** - Filter by owners, recipients, tags, block heights
- **Tag Filtering** - Complex tag queries with EQ/NEQ operators
- **Cursor-based Pagination** - Efficient pagination following GraphQL best practices
- **Sorting** - Sort by block height (ascending/descending)
- **Data Transformation** - Automatic base64url decoding, winston/AR conversion

## Installation

### Option 1: Local Installation

1. Install Python 3.7 or higher
2. Clone or download this directory
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Option 2: Docker

1. Build and run with Docker:
   ```bash
   docker build -t arlocal-python .
   docker run -p 1984:1984 arlocal-python
   ```

2. Or use docker-compose:
   ```bash
   docker-compose up
   ```

## Usage

### Basic Usage

1. Start the server:
   ```bash
   python app.py
   ```

2. Or use the startup script with options:
   ```bash
   python start.py --port 1984 --debug
   ```

3. The server will start on `http://localhost:1984`

4. Test the server:
   ```bash
   curl http://localhost:1984/
   ```

5. Run the test suite:
   ```bash
   python test_api.py
   ```

### Configuration

You can configure the server using environment variables:

```bash
export ARLOCAL_HOST=0.0.0.0
export ARLOCAL_PORT=1984
export ARLOCAL_DEBUG=true
export ARLOCAL_NETWORK=arlocal
export ARLOCAL_LOG_LEVEL=INFO
python app.py
```

Available configuration options:
- `ARLOCAL_HOST`: Host to bind to (default: 0.0.0.0)
- `ARLOCAL_PORT`: Port to run on (default: 1984)
- `ARLOCAL_DEBUG`: Enable debug mode (default: false)
- `ARLOCAL_NETWORK`: Network name (default: arlocal)
- `ARLOCAL_VERSION`: Network version (default: 5)
- `ARLOCAL_RELEASE`: Network release (default: 53)
- `ARLOCAL_PRICE_PER_KB`: Price per KB in winston (default: 65595508)
- `ARLOCAL_FAIL_RATE`: Mining failure rate (default: 0.1)
- `ARLOCAL_LOG_LEVEL`: Logging level (default: INFO)

## Implementation Notes

This is a simplified implementation focused on providing the REST API endpoints. Key differences from the original:

- **In-memory storage**: All data is stored in memory (no SQLite database)
- **Simplified validation**: Basic validation without full Arweave cryptographic verification
- **Mock data**: Some endpoints return mock data for demonstration purposes
- **No persistence**: Data is lost when the server restarts
- **Basic GraphQL**: GraphQL endpoint is a placeholder without full query support

## API Compatibility

The endpoints are designed to be compatible with the original ArLocal API, making it suitable for:
- Testing Arweave applications
- Development environments
- Learning about Arweave transaction structure
- Prototyping Arweave integrations

## Example Usage

### Create a wallet:
```bash
curl -X POST http://localhost:1984/wallet \
  -H "Content-Type: application/json" \
  -d '{"balance": 1000000}'
```

### Submit a transaction:
```bash
curl -X POST http://localhost:1984/tx \
  -H "Content-Type: application/json" \
  -d '{
    "owner": "your-owner-key",
    "target": "target-address", 
    "quantity": "0",
    "reward": "1000",
    "last_tx": "",
    "tags": [],
    "signature": "signature",
    "data": "Hello World"
  }'
```

### Mine a block:
```bash
curl http://localhost:1984/mine/1
```

### Get network status:
```bash
curl http://localhost:1984/
```

## Development

To extend this implementation:

1. Add database persistence (SQLite, PostgreSQL, etc.)
2. Implement full Arweave cryptographic validation
3. Add complete GraphQL resolver implementation
4. Add transaction bundling support
5. Implement proper chunk merkle tree validation
6. Add rate limiting and security features

## License

This implementation is provided as-is for educational and development purposes.
