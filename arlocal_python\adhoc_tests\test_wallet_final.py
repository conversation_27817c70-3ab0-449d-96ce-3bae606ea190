#!/usr/bin/env python3
"""
Final test to verify wallet behavior matches original arlocal exactly
"""

import requests
import json
import sys

def test_basic_wallet_behavior():
    """Test the core wallet behavior that was reported as broken"""
    base_url = "http://localhost:1984"
    
    # Use the exact address from the original arlocal test
    test_address = "pLPrkgQBFCpaJm1DIksYSPaeMNy6evqoLPCPAlZ-YG8"
    
    print("Testing core wallet behavior...")
    print("=" * 50)
    
    try:
        # Reset first to ensure clean state
        reset_response = requests.get(f"{base_url}/reset")
        if reset_response.status_code != 200:
            print(f"❌ Reset failed: {reset_response.status_code}")
            return False
        print("✅ Network reset successful")
        
        # Test 1: Non-existent wallet should return '0'
        response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        if response.status_code != 200:
            print(f"❌ Balance check failed: status {response.status_code}")
            return False
        
        if response.text != "0":
            print(f"❌ Expected '0' for non-existent wallet, got '{response.text}'")
            return False
        print("✅ Non-existent wallet returns '0'")
        
        # Test 2: Mint tokens
        mint_response = requests.get(f"{base_url}/mint/{test_address}/100")
        if mint_response.status_code != 200:
            print(f"❌ Mint failed: status {mint_response.status_code}")
            return False
        
        if mint_response.text != "100":
            print(f"❌ Expected '100' from mint, got '{mint_response.text}'")
            return False
        print("✅ Mint returns '100'")
        
        # Test 3: Check balance after mint
        balance_response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        if balance_response.status_code != 200:
            print(f"❌ Balance check after mint failed: status {balance_response.status_code}")
            return False
        
        if balance_response.text != "100":
            print(f"❌ Expected '100' after mint, got '{balance_response.text}'")
            return False
        print("✅ Balance after mint is '100'")
        
        # Test 4: Reset should clear wallet
        reset_response2 = requests.get(f"{base_url}/reset")
        if reset_response2.status_code != 200:
            print(f"❌ Second reset failed: {reset_response2.status_code}")
            return False
        print("✅ Second reset successful")
        
        # Test 5: Balance should be 0 after reset
        balance_after_reset = requests.get(f"{base_url}/wallet/{test_address}/balance")
        if balance_after_reset.status_code != 200:
            print(f"❌ Balance check after reset failed: status {balance_after_reset.status_code}")
            return False
        
        if balance_after_reset.text != "0":
            print(f"❌ Expected '0' after reset, got '{balance_after_reset.text}'")
            return False
        print("✅ Balance after reset is '0'")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_address_validation_behavior():
    """Test that address validation works like original arlocal"""
    base_url = "http://localhost:1984"
    
    print("\nTesting address validation behavior...")
    print("=" * 50)
    
    try:
        # Test with any address format - should work (no validation in balance route)
        weird_address = "this-is-not-a-valid-arweave-address-but-should-work"
        response = requests.get(f"{base_url}/wallet/{weird_address}/balance")
        
        if response.status_code != 200:
            print(f"❌ Weird address failed: status {response.status_code}")
            return False
        
        if response.text != "0":
            print(f"❌ Expected '0' for weird address, got '{response.text}'")
            return False
        
        print("✅ Any address format works in balance route (no validation)")
        
        # Test mint with weird address
        mint_response = requests.get(f"{base_url}/mint/{weird_address}/50")
        if mint_response.status_code != 200:
            print(f"❌ Mint with weird address failed: status {mint_response.status_code}")
            return False
        
        if mint_response.text != "50":
            print(f"❌ Expected '50' from mint with weird address, got '{mint_response.text}'")
            return False
        
        print("✅ Mint works with any address format")
        
        # Check balance
        balance_response = requests.get(f"{base_url}/wallet/{weird_address}/balance")
        if balance_response.text != "50":
            print(f"❌ Expected '50' for weird address after mint, got '{balance_response.text}'")
            return False
        
        print("✅ Balance check works with any address format")
        
        return True
        
    except Exception as e:
        print(f"❌ Address validation test failed: {e}")
        return False

if __name__ == "__main__":
    print("ArLocal Python Wallet Final Test")
    print("Testing the exact issue reported by the user")
    print("=" * 60)
    
    # Test 1: Core wallet behavior
    test1_passed = test_basic_wallet_behavior()
    
    # Test 2: Address validation behavior
    test2_passed = test_address_validation_behavior()
    
    print("\n" + "=" * 60)
    print("FINAL TEST SUMMARY:")
    print(f"Core wallet behavior: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Address validation: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The wallet handling issue has been fixed!")
        print("✅ /wallet/<address>/balance now returns '0' for non-existent wallets")
        print("✅ No more 404 errors for valid addresses")
        print("✅ Behavior now matches original arlocal exactly")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
