"""
Transactions routes blueprint for ArLocal Python Implementation
Handles all transaction-related routes including chunks and data access
"""

from flask import Blueprint, request, jsonify, Response, current_app
import logging
from utils import (
    validate_txid, calculate_price, save_transaction_data, load_transaction_data,
    calculate_transaction_id, b64url_to_bytes, bytes_to_b64url
)

# Create blueprint
transactions_bp = Blueprint('transactions', __name__)

# Get logger
logger = logging.getLogger(__name__)

# Logs are accessed via current_app.logs

# Transaction Anchor Route
@transactions_bp.route('/tx_anchor', methods=['GET'])
def get_tx_anchor():
    """Get transaction anchor (latest block ID)"""
    db = current_app.db
    
    latest_block = db.get_latest_block()
    anchor = latest_block['id'] if latest_block else ''
    return Response(anchor, mimetype='text/plain')

# Transaction Routes
@transactions_bp.route('/tx/pending', methods=['GET'])
def get_pending_transactions():
    """Get list of pending transaction IDs"""
    db = current_app.db
    return jsonify(db.get_pending_transactions())

@transactions_bp.route('/tx/<txid>', methods=['GET'])
def get_transaction(txid: str):
    """Get transaction by ID"""
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    current_app.logs.append(f"Retrieved transaction: {txid}")
    return jsonify(transaction)

@transactions_bp.route('/tx/<txid>/status', methods=['GET'])
def get_transaction_status(txid: str):
    """Get transaction status"""
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return jsonify({"status": "Pending"})

    # Get current network height for confirmations
    network_info = db.get_network_info()
    current_height = network_info['height']

    # Return status information
    return jsonify({
        "block_indep_hash": transaction['block'],
        "block_height": transaction.get('height', 0),
        "number_of_confirmations": current_height - transaction.get('height', 0)
    })

@transactions_bp.route('/tx/<txid>/offset', methods=['GET'])
def get_transaction_offset(txid: str):
    """Get transaction offset information"""
    import random
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    data_size = int(transaction.get('data_size', 0))

    # Simplified offset calculation
    offset = random.randint(1000, 10000)  # Mock offset

    return jsonify({
        "offset": str(offset + data_size - 1),
        "size": str(data_size)
    })

@transactions_bp.route('/tx/<txid>/data', methods=['GET'])
def get_transaction_raw_data(txid: str):
    """Get raw transaction data as base64url encoded string"""
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Check for the data_size limit (same as original ArLocal)
    data_size = int(transaction.get('data_size', 0))
    if data_size > 12000000:  # 12MB limit
        return Response('tx_data_too_big', mimetype='text/plain'), 400

    # Load data from file or reconstruct from chunks
    data = get_transaction_data(transaction, txid)
    if data is not None:
        # Convert binary data to base64url encoded string
        if isinstance(data, bytes):
            b64url_data = bytes_to_b64url(data)
        else:
            # If data is already a string (base64url), return as-is
            b64url_data = str(data)

        return Response(b64url_data, mimetype='text/plain')

    return jsonify({"status": 404, "error": "Data not found"}), 404

@transactions_bp.route('/tx/<txid>/data.<ext>', methods=['GET'])
def get_transaction_data_with_extension(txid: str, ext: str):
    """Get transaction data with specific extension"""
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Determine content type based on extension
        content_type = 'application/octet-stream'
        if ext == 'json':
            content_type = 'application/json'
        elif ext == 'html':
            content_type = 'text/html'
        elif ext == 'txt':
            content_type = 'text/plain'

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404

# Transaction field and file routes
VALID_FIELDS = ['id', 'last_tx', 'owner', 'tags', 'target', 'quantity', 'data_root', 'data_size', 'reward', 'signature']

@transactions_bp.route('/tx/<txid>/<field>', methods=['GET'])
def get_transaction_field(txid: str, field: str):
    """Get specific transaction field"""
    db = current_app.db
    
    # Check if it's a file request (contains a dot)
    if '.' in field:
        return get_transaction_file(txid, field)

    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    if field not in VALID_FIELDS:
        return jsonify({"status": 404, "error": "Field Not Found !"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if field in transaction:
        return Response(str(transaction[field]), mimetype='text/plain')

    return jsonify({"status": 404, "error": "Field not found in transaction"}), 404

def get_transaction_file(txid: str, filename: str):
    """Get transaction file (helper function)"""
    db = current_app.db
    
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return Response('Pending', mimetype='text/plain')

    # Return the data if available
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        return Response(data, mimetype='application/octet-stream')

    return jsonify({"status": 404, "error": "File not found"}), 404

@transactions_bp.route('/tx', methods=['POST'])
def post_transaction():
    """Submit a new transaction"""
    try:
        db = current_app.db

        data = request.get_json()
        if not data:
            return jsonify({"status": 400, "error": "Invalid JSON data"}), 400

        # Basic validation
        required_fields = ['owner', 'target', 'quantity', 'reward', 'last_tx', 'tags', 'signature']
        for field in required_fields:
            if field not in data:
                return jsonify({"status": 400, "error": f"Missing required field: {field}"}), 400

        # Calculate transaction ID from signature (as per Arweave protocol)
        if 'signature' not in data or not data['signature']:
            return jsonify({"status": 400, "error": "Signature is required for transaction ID calculation"}), 400

        txid = calculate_transaction_id(data['signature'])

        # If transaction ID is provided in the request, validate it matches the calculated one
        if 'id' in data and data['id'] != txid:
            return jsonify({"status": 400, "error": "Transaction ID does not match signature hash"}), 400

        # Calculate reward based on data size
        data_size = int(data.get('data_size', 0))
        calculated_reward = calculate_price(data_size, current_app.config['DEFAULT_PRICE_PER_KB'])

        # Check if owner has enough balance
        owner_address = data.get('owner', '')
        current_balance = db.get_wallet_balance(owner_address)

        if current_balance < calculated_reward:
            # Create wallet with sufficient balance if it doesn't exist
            if current_balance == 0:
                db.create_wallet(owner_address, calculated_reward * 10)
            else:
                return jsonify({"code": 410, "msg": "You don't have enough tokens"}), 410

        # Prepare transaction data
        transaction_data = {
            "id": txid,
            "owner": data['owner'],
            "target": data['target'],
            "quantity": data['quantity'],
            "reward": data['reward'],
            "last_tx": data['last_tx'],
            "tags": data['tags'],
            "signature": data['signature'],
            "data_size": data_size,
            "data_root": data.get('data_root', ''),
            "format": data.get('format', 2),
            "owner_address": owner_address
        }

        # Store transaction data if provided (for small transactions with embedded data)
        # For large transactions, data will be uploaded as chunks separately
        if 'data' in data and data['data']:
            # Data comes in as base64url encoded string, decode it for storage
            try:
                decoded_data = b64url_to_bytes(data['data'])
                save_transaction_data(db.data_dir, txid, decoded_data)
            except Exception as e:
                logger.error(f"Error decoding transaction data: {e}")
                # Store as-is if decoding fails
                save_transaction_data(db.data_dir, txid, data['data'])
        elif data.get('data_root') and data.get('data_size'):
            # Large transaction - data will be uploaded as chunks
            # Validate that data_root and data_size are provided for chunk-based transactions
            logger.info(f"Transaction {txid} expects data to be uploaded as chunks (data_root: {data['data_root']}, data_size: {data['data_size']})")

        # Insert transaction into database
        if db.insert_transaction(transaction_data):
            # Deduct fee from wallet balance
            fee = max(int(data.get('reward', 0)), calculated_reward)
            new_balance = db.get_wallet_balance(owner_address) - fee
            db.update_wallet_balance(owner_address, new_balance)

            current_app.logs.append(f"Transaction submitted: {txid}")
            return Response(txid, mimetype='text/plain')
        else:
            return jsonify({"status": 500, "error": "Failed to insert transaction"}), 500

    except Exception as e:
        logger.error(f"Error posting transaction: {e}")
        return jsonify({"status": 500, "error": str(e)}), 500

@transactions_bp.route('/tx/<txid>', methods=['DELETE'])
def delete_transaction(txid: str):
    """Delete a transaction"""
    db = current_app.db

    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Delete transaction from database
    if db.delete_transaction(txid):
        current_app.logs.append(f"Transaction deleted: {txid}")
        return jsonify({"status": 200, "message": "Transaction deleted"})
    else:
        return jsonify({"status": 500, "error": "Failed to delete transaction"}), 500

# Chunk Routes
@transactions_bp.route('/chunk', methods=['POST'])
def post_chunk():
    """Submit chunk data"""
    try:
        db = current_app.db

        data = request.get_json()
        if not data or 'chunk' not in data:
            return jsonify({"status": 400, "error": "Invalid chunk data"}), 400

        # Validate required fields for chunk
        required_chunk_fields = ['chunk', 'data_root', 'data_size']
        for field in required_chunk_fields:
            if field not in data:
                return jsonify({"status": 400, "error": f"Missing required chunk field: {field}"}), 400

        # Calculate offset - in the original arlocal, this is calculated based on existing chunks
        # For this implementation, we'll use a simpler approach
        offset = db.get_next_chunk_offset()

        # Store chunk
        chunk_data = {
            'chunk': data['chunk'],
            'data_root': data['data_root'],
            'data_size': int(data['data_size']),
            'offset': offset,
            'data_path': data.get('data_path', '')
        }

        if db.insert_chunk(chunk_data):
            current_app.logs.append(f"Chunk stored for data_root {data['data_root']} at offset: {offset}")
            return jsonify({})
        else:
            return jsonify({"error": "Failed to store chunk"}), 500

    except Exception as e:
        logger.error(f"Error posting chunk: {e}")
        return jsonify({"error": str(e)}), 500

@transactions_bp.route('/chunk/<int:offset>', methods=['GET'])
def get_chunk_by_offset_route(offset: int):
    """Get chunk by offset"""
    db = current_app.db

    chunk = db.get_chunk_by_offset(offset)
    if not chunk:
        return '', 204  # No Content

    return jsonify(chunk)

# Data Routes (for transaction data access)
@transactions_bp.route('/<txid>', methods=['GET', 'HEAD'])
@transactions_bp.route('/<txid>/<path:subpath>', methods=['GET', 'HEAD'])
def get_data_route(txid: str, subpath: str = None):
    """Get transaction data by transaction ID"""
    db = current_app.db

    if not validate_txid(txid):
        return jsonify({"status": 404, "error": "Not Found"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    if request.method == 'HEAD':
        # Return headers only
        response = Response()
        response.headers['Content-Length'] = str(transaction.get('data_size', 0))

        # Check if data exists (either as file or reconstructable from chunks)
        data = get_transaction_data(transaction, txid)
        if data is not None:
            response.headers['Content-Type'] = 'application/octet-stream'
        return response

    # GET request - return data
    data = get_transaction_data(transaction, txid)
    if data is not None:
        # Determine content type from transaction tags
        content_type = 'application/octet-stream'
        if 'tags' in transaction and transaction['tags']:
            tags = transaction['tags']
            for tag in tags:
                try:
                    # Decode tag name and value from base64url
                    tag_name = b64url_to_bytes(tag.get('name', '')).decode('utf-8')
                    tag_value = b64url_to_bytes(tag.get('value', '')).decode('utf-8')
                    if tag_name.lower() == 'content-type':
                        content_type = tag_value
                        break
                except Exception:
                    # If decoding fails, try direct comparison (fallback)
                    if tag.get('name') == 'Content-Type':
                        content_type = tag.get('value', content_type)
                        break

        # Ensure data is bytes for binary response
        if isinstance(data, str):
            # If data is a string (base64url), decode it
            try:
                data = b64url_to_bytes(data)
            except Exception as e:
                logger.error(f"Error decoding data string: {e}")
                data = data.encode('utf-8')  # Fallback to UTF-8 encoding

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404


def get_transaction_data(transaction: dict, txid: str) -> bytes:
    """Get transaction data, either from file or by reconstructing from chunks"""
    db = current_app.db

    # First try to load data from file (for transactions with embedded data)
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Ensure we return bytes
        if isinstance(data, str):
            # If it's a string, try to decode as base64url first, then fallback to UTF-8
            try:
                return b64url_to_bytes(data)
            except Exception:
                return data.encode('utf-8')
        return data

    # If no file data, try to reconstruct from chunks using data_root
    data_root = transaction.get('data_root', '')
    data_size = int(transaction.get('data_size', 0))

    if data_root and data_size > 0:
        # Get chunks for this data_root
        chunks = db.get_chunks_by_data_root(data_root)
        if chunks:
            # Sort chunks by offset and reconstruct data
            chunks.sort(key=lambda x: x.get('offset', 0))
            reconstructed_data = b''

            for chunk in chunks:
                try:
                    # Decode chunk data from base64url
                    chunk_data = b64url_to_bytes(chunk['chunk'])
                    reconstructed_data += chunk_data
                except Exception as e:
                    logger.error(f"Error decoding chunk: {e}")
                    continue

            # Verify reconstructed data size matches expected
            if len(reconstructed_data) == data_size:
                return reconstructed_data
            else:
                logger.warning(f"Reconstructed data size {len(reconstructed_data)} doesn't match expected {data_size}")

    return None
